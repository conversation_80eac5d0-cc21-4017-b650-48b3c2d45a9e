vercel env add NEXT_PUBLIC_SUPABASE_URL production
vercel env add NEXT_PUBLIC_SUPABASE_ANON_KEY production
vercel env add SUPABASE_SERVICE_ROLE_KEY production
vercel env add DATABASE_URL production
vercel env add N8N_HOST production
vercel env add N8N_API_KEY production
vercel env add N8N_WEBHOOK_SECRET production
vercel env add OPENAI_API_KEY production
vercel env add OPENAI_MODEL production
vercel env add OPENROUTER_API_KEY production
vercel env add OPENROUTER_BASE_URL production
vercel env add SITE_URL production
vercel env add CONTENT_GENERATION_ENABLED production
vercel env add SCRAPE_DO_API_KEY production
vercel env add SCRAPE_DO_BASE_URL production
vercel env add SCRAPE_DO_TIMEOUT production
vercel env add SCRAPE_DO_RETRY_ATTEMPTS production
vercel env add PUPPETEER_EXECUTABLE_PATH production
vercel env add SCREENSHOT_QUALITY production
vercel env add MAX_SCRAPE_TIMEOUT production
vercel env add GOOGLE_ANALYTICS_ID production
vercel env add SMTP_HOST production
vercel env add SMTP_PORT production
vercel env add SMTP_USER production
vercel env add SMTP_PASS production
vercel env add JWT_SECRET production
vercel env add ADMIN_API_KEY production
vercel env add NEXT_PUBLIC_ADMIN_API_KEY production
vercel env add ENCRYPTION_KEY production
vercel env add VERCEL_URL production
vercel env add VERCEL_TOKEN production
vercel env add NEXT_PUBLIC_VERCEL_URL production
vercel env add VERCEL_ENV production
vercel env add NEXT_PUBLIC_SITE_URL production
vercel env add CANONICAL_URL production
vercel env add NODE_ENV production
vercel env add NEXT_TELEMETRY_DISABLED production
vercel env add SCRAPING_ENABLED production
vercel env add ENHANCED_SCRAPING_ENABLED production
vercel env add ENABLE_BULK_PROCESSING production
vercel env add ENABLE_ADMIN_PANEL production
vercel env add ENABLE_API_ENDPOINTS production
vercel env add ENABLE_SEARCH_FUNCTIONALITY production
vercel env add ENABLE_RATE_LIMITING production
vercel env add RATE_LIMIT_REQUESTS_PER_MINUTE production
vercel env add ENABLE_CACHING production
vercel env add CACHE_TTL_SECONDS production
vercel env add DATABASE_POOL_MIN production
vercel env add DATABASE_POOL_MAX production
vercel env add DATABASE_TIMEOUT production