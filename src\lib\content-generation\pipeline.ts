/**
 * Content Generation Pipeline with Editorial Controls
 * 
 * This module orchestrates the complete content generation workflow including:
 * - AI content generation
 * - Content validation
 * - Editorial review workflow
 * - Quality scoring
 * - Approval management
 */

import { AIContentGenerator } from '../ai/content-generator';
import { ContentValidator } from './validator';
import { QualityScorer } from './quality-scorer';
import { EditorialControls } from './editorial-controls';
import { ManualReview } from './manual-review';
import { supabaseAdmin } from '../supabase';
import { PromptManager } from '../ai/prompt-manager';
import {
  ContentGenerationRequest,
  EditorialReview,
  InternalEditorialReview,
  PipelineStatus,
  WorkflowState,
  ValidationResult,
  QualityScore
} from '../types';

export interface PipelineOptions {
  skipValidation?: boolean;
  requireEditorialReview?: boolean;
  autoApprove?: boolean;
  qualityThreshold?: number;
  priority?: 'high' | 'normal' | 'low';
  customPrompts?: {
    systemPrompt?: string;
    userPrompt?: string;
  };
}

export interface PipelineResult {
  success: boolean;
  toolId: string;
  status: PipelineStatus;
  workflowState: WorkflowState;
  generatedContent?: any;
  validation?: ValidationResult;
  qualityScore?: QualityScore;
  editorialReview?: InternalEditorialReview;
  error?: string;
  metadata: {
    startTime: string;
    endTime?: string;
    duration?: number;
    modelUsed?: string;
    tokenUsage?: any;
    steps: string[];
  };
}

export class ContentGenerationPipeline {
  private aiGenerator: AIContentGenerator;
  private validator: ContentValidator;
  private qualityScorer: QualityScorer;
  private editorialControls: EditorialControls;
  private manualReview: ManualReview;

  constructor() {
    this.aiGenerator = new AIContentGenerator();
    this.validator = new ContentValidator();
    this.qualityScorer = new QualityScorer();
    this.editorialControls = new EditorialControls();
    this.manualReview = new ManualReview();
  }

  /**
   * Main pipeline execution method
   */
  async execute(
    request: ContentGenerationRequest,
    options: PipelineOptions = {}
  ): Promise<PipelineResult> {
    const startTime = new Date().toISOString();
    const steps: string[] = [];
    
    try {
      console.log(`Starting content generation pipeline for tool: ${request.toolId}`);
      
      // Step 1: Initialize workflow state and check if tool is from bulk processing
      steps.push('initialize_workflow');
      const workflowState = await this.initializeWorkflow(request, options);
      const isBulkProcessing = await this.isBulkProcessingTool(request.toolId);

      // Step 2: Generate AI content
      steps.push('ai_generation');
      const generationResult = await this.generateContent(request, options);
      
      if (!generationResult.success) {
        throw new Error(`AI generation failed: ${generationResult.error}`);
      }

      // Step 3: Validate content (unless skipped)
      let validation: ValidationResult | undefined;
      if (!options.skipValidation) {
        steps.push('content_validation');
        validation = await this.validator.validateContent(
          generationResult.content!,
          request.scrapedContent,
          request.toolUrl
        );
      }

      // Step 4: Calculate quality score
      steps.push('quality_scoring');
      const qualityScore = await this.qualityScorer.scoreContent(
        generationResult.content!,
        validation
      );

      // Step 5: Determine workflow based on bulk processing vs user submission
      let editorialReview: InternalEditorialReview | undefined;
      let finalStatus: PipelineStatus;
      let finalWorkflowState: WorkflowState;

      if (isBulkProcessing) {
        // Bulk processing: bypass manual review and publish directly
        steps.push('bulk_processing_auto_publish');
        console.log(`🚀 Bulk processing detected - bypassing manual review and publishing directly`);

        // Parse and apply the generated content to individual database columns
        const parsedContent = await this.parseGeneratedContent(generationResult.content!);

        await this.updateToolRecord(request.toolId, {
          ...parsedContent, // Apply parsed content to individual columns
          generated_content: generationResult.content,
          ai_generation_status: 'completed',
          content_status: 'published',
          published_at: new Date().toISOString(),
          content_quality_score: qualityScore.overall,
          last_ai_update: new Date().toISOString()
        });

        finalStatus = 'completed';
        finalWorkflowState = 'published';
      } else {
        // User submission: follow normal review workflow
        const needsEditorialReview = this.shouldRequireEditorialReview(
          qualityScore,
          options,
          validation
        );

        if (needsEditorialReview) {
          steps.push('editorial_review_required');

          // Create editorial review record
          editorialReview = await this.editorialControls.createReview({
            toolId: request.toolId,
            generatedContent: generationResult.content!,
            qualityScore: qualityScore.overall,
            validationResult: validation,
            priority: options.priority || 'normal'
          });

          finalStatus = 'pending_review';
          finalWorkflowState = 'editorial_review';

          // Parse and apply the generated content to individual database columns
          const parsedContent = await this.parseGeneratedContent(generationResult.content!);

          // Update tool record with editorial review
          await this.updateToolRecord(request.toolId, {
            ...parsedContent, // Apply parsed content to individual columns
            generated_content: generationResult.content,
            ai_generation_status: finalStatus,
            content_quality_score: qualityScore.overall,
            last_ai_update: new Date().toISOString(),
            editorial_review_id: editorialReview?.id
          });
        } else if (options.autoApprove && qualityScore.overall >= (options.qualityThreshold || 80)) {
          steps.push('auto_approval');

          // Auto-approve high-quality content
          await this.autoApproveContent(request.toolId, generationResult.content!);

          finalStatus = 'completed';
          finalWorkflowState = 'published';
        } else {
          steps.push('manual_review_required');

          finalStatus = 'pending_manual_review';
          finalWorkflowState = 'manual_review';

          // Parse and apply the generated content to individual database columns
          const parsedContent = this.parseGeneratedContent(generationResult.content!);

          // Update tool record for manual review
          await this.updateToolRecord(request.toolId, {
            ...parsedContent, // Apply parsed content to individual columns
            generated_content: generationResult.content,
            ai_generation_status: finalStatus,
            content_quality_score: qualityScore.overall,
            last_ai_update: new Date().toISOString()
          });
        }
      }

      const endTime = new Date().toISOString();
      const duration = new Date(endTime).getTime() - new Date(startTime).getTime();

      return {
        success: true,
        toolId: request.toolId,
        status: finalStatus,
        workflowState: finalWorkflowState,
        generatedContent: generationResult.content,
        validation,
        qualityScore,
        editorialReview,
        metadata: {
          startTime,
          endTime,
          duration,
          modelUsed: generationResult.modelUsed?.model,
          tokenUsage: generationResult.tokenUsage,
          steps
        }
      };

    } catch (error: any) {
      console.error('Content generation pipeline failed:', error);
      
      const endTime = new Date().toISOString();
      const duration = new Date(endTime).getTime() - new Date(startTime).getTime();

      return {
        success: false,
        toolId: request.toolId,
        status: 'failed',
        workflowState: 'error',
        error: error.message,
        metadata: {
          startTime,
          endTime,
          duration,
          steps
        }
      };
    }
  }

  /**
   * Initialize workflow state for the content generation process
   */
  private async initializeWorkflow(
    request: ContentGenerationRequest,
    options: PipelineOptions
  ): Promise<WorkflowState> {
    // Update tool status to processing
    await this.updateToolRecord(request.toolId, {
      ai_generation_status: 'processing',
      last_ai_update: new Date().toISOString()
    });

    return 'ai_generation';
  }

  /**
   * Generate content using AI Dude methodology
   */
  private async generateContent(
    request: ContentGenerationRequest,
    options: PipelineOptions
  ) {
    const generationOptions = {
      complexity: request.complexity || 'medium',
      priority: request.priority || 'quality',
      contentQuality: request.contentQuality || 70,
      scrapingCost: request.scrapingCost || 0,
      maxRetries: 3,
      customPrompts: options.customPrompts,
      aiProvider: request.aiProvider // Pass through user's provider choice
    };

    // Use AI Dude methodology (sole methodology)
    return await this.aiGenerator.generateContent(
      request.scrapedContent,
      request.toolUrl,
      generationOptions
    );
  }

  /**
   * Determine if editorial review is required
   */
  private shouldRequireEditorialReview(
    qualityScore: QualityScore,
    options: PipelineOptions,
    validation?: ValidationResult
  ): boolean {
    // Always require review if explicitly requested
    if (options.requireEditorialReview) {
      return true;
    }

    // Require review for low-quality content
    if (qualityScore.overall < 70) {
      return true;
    }

    // Require review if validation failed
    if (validation && !validation.isValid) {
      return true;
    }

    // Require review for content with specific flags
    if (qualityScore.flags && qualityScore.flags.length > 0) {
      return true;
    }

    return false;
  }

  /**
   * Auto-approve high-quality content
   */
  private async autoApproveContent(toolId: string, content: any): Promise<void> {
    await this.updateToolRecord(toolId, {
      content_status: 'published',
      published_at: new Date().toISOString(),
      generated_content: content
    });
  }

  /**
   * Check if tool is from bulk processing with enhanced error handling and retry logic
   */
  private async isBulkProcessingTool(toolId: string): Promise<boolean> {
    const maxRetries = 5; // Increased from 3 to 5 for better race condition handling
    const retryDelay = 1000; // 1 second base delay

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`🔍 Checking bulk processing status for tool: ${toolId} (attempt ${attempt}/${maxRetries})`);

        // Validate tool ID format
        if (!toolId || typeof toolId !== 'string' || toolId.trim().length === 0) {
          console.warn(`❌ Invalid tool ID format: ${toolId}`);
          return false;
        }

        // Add progressive delay to allow for database consistency
        // This helps with race conditions where tool was just created
        if (attempt === 1) {
          await new Promise(resolve => setTimeout(resolve, 750)); // Increased to 750ms initial delay
        }

        // First, check if tool exists and handle multiple/no rows gracefully
        // Use admin client for server-side operations to ensure proper permissions
        console.log(`🔧 DEBUG: supabaseAdmin available: ${!!supabaseAdmin}`);
        console.log(`🔧 DEBUG: SUPABASE_SERVICE_ROLE_KEY available: ${!!process.env.SUPABASE_SERVICE_ROLE_KEY}`);

        if (!supabaseAdmin) {
          console.warn(`❌ Admin client not available - missing SUPABASE_SERVICE_ROLE_KEY`);
          console.warn(`Could not determine submission source for tool ${toolId}, defaulting to user submission workflow`);
          return false;
        }

        console.log(`🔧 DEBUG: Executing query with admin client for tool: ${toolId.trim()}`);
        const { data: tools, error: queryError } = await supabaseAdmin
          .from('tools')
          .select('submission_source, submission_type, name, website, id')
          .eq('id', toolId.trim());
        console.log(`🔧 DEBUG: Query result - tools: ${tools?.length || 0}, error: ${queryError?.message || 'none'}`);

        if (queryError) {
          console.warn(`❌ Database query error for tool ${toolId} (attempt ${attempt}): ${queryError.message}`);

          // If this is the last attempt, log detailed error and return false
          if (attempt === maxRetries) {
            console.warn(`❌ Final attempt failed for tool ${toolId}. Error details:`, {
              code: queryError.code,
              message: queryError.message,
              details: queryError.details,
              hint: queryError.hint
            });
            console.warn(`Could not determine submission source for tool ${toolId}, defaulting to user submission workflow`);
            return false;
          }

          // Wait before retry
          await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
          continue;
        }

        if (!tools || tools.length === 0) {
          console.warn(`❌ Tool ${toolId} not found in database (attempt ${attempt})`);

          // For "not found" errors, always retry - this is likely a race condition
          if (attempt < maxRetries) {
            console.warn(`🔄 Retrying in ${retryDelay * attempt}ms - likely race condition with tool creation`);
            await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
            continue;
          }

          // Only give up after all retries exhausted
          console.warn(`❌ Tool ${toolId} not found after ${maxRetries} attempts`);
          console.warn(`Could not determine submission source for tool ${toolId}, defaulting to user submission workflow`);
          return false;
        }

        if (tools.length > 1) {
          console.warn(`🚨 CRITICAL: Multiple tools found with ID ${toolId} (${tools.length} rows)`);
          console.warn(`This indicates a database integrity issue - using first match`);
          console.warn(`Database admin should investigate duplicate primary keys`);

          // Log all duplicate tools for debugging
          tools.forEach((tool, index) => {
            console.warn(`  Tool ${index + 1}: ${tool.name} (${tool.website}) - ${tool.submission_source}`);
          });
        }

        // Use the first (or only) tool
        const tool = tools[0];

        // Validate tool data
        if (!tool.submission_source) {
          console.warn(`⚠️ Tool ${toolId} has null/undefined submission_source, defaulting to user submission workflow`);
          return false;
        }

        console.log(`📋 Tool details: ${tool.name} (${tool.website})`);
        console.log(`📝 Submission type: ${tool.submission_type}`);
        console.log(`📋 Submission source: ${tool.submission_source}`);

        const isBulkProcessing = tool.submission_source === 'bulk_processing';
        console.log(`🎯 Bulk processing detection result: ${isBulkProcessing ? 'YES - will bypass manual review' : 'NO - will go to manual review'}`);

        return isBulkProcessing;
      } catch (error) {
        console.warn(`💥 Exception checking bulk processing status for tool ${toolId} (attempt ${attempt}):`, error);

        // If this is the last attempt, return false
        if (attempt === maxRetries) {
          console.warn(`Could not determine submission source for tool ${toolId} after ${maxRetries} attempts, defaulting to user submission workflow`);
          return false;
        }

        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
      }
    }

    // This should never be reached, but just in case
    console.warn(`Unexpected end of retry loop for tool ${toolId}, defaulting to user submission workflow`);
    return false;
  }

  /**
   * Parse generated content and extract individual database fields
   */
  private async parseGeneratedContent(generatedContent: any): Promise<any> {
    try {
      // Use PromptManager to process the AI Dude response and map to database schema
      const parsedContent = PromptManager.processAIDudeResponse(generatedContent);

      console.log(`📝 Parsed generated content into ${Object.keys(parsedContent).length} database fields`);

      // Validate and ensure category_id exists (create if needed)
      if (parsedContent.category_id) {
        console.log(`🔍 Ensuring category exists: ${parsedContent.category_id}`);
        parsedContent.category_id = await this.validateAndFixCategoryId(parsedContent.category_id);
        console.log(`✅ Category ready: ${parsedContent.category_id}`);
      } else {
        console.log('ℹ️ No category_id provided in generated content');
      }

      // Filter to only include fields that exist in the database schema
      // Based on the actual tools table schema from migrations
      const validDatabaseFields = [
        'name', 'description', 'short_description', 'detailed_description',
        'company', 'category_id', 'subcategory', 'features', 'pricing',
        'pros_and_cons', 'social_links', 'hashtags', 'tooltip', 'haiku', 'releases',
        'faqs', 'meta_title', 'meta_description', 'meta_keywords'
      ];

      const filteredFields: any = {};
      validDatabaseFields.forEach(field => {
        if (parsedContent[field] !== undefined) {
          filteredFields[field] = parsedContent[field];
        }
      });

      console.log(`📊 Filtered to ${Object.keys(filteredFields).length} valid database fields`);

      return filteredFields;
    } catch (error) {
      console.warn(`Failed to parse generated content:`, error);
      return {}; // Return empty object if parsing fails
    }
  }

  /**
   * Validate category_id and create category if it doesn't exist
   */
  private async validateAndFixCategoryId(categoryId: string): Promise<string> {
    if (!supabaseAdmin) {
      console.warn('Admin client not available - cannot validate category_id');
      return categoryId;
    }

    try {
      // Check if category exists
      const { data: existingCategory, error: checkError } = await supabaseAdmin
        .from('categories')
        .select('id')
        .eq('id', categoryId)
        .single();

      if (checkError && checkError.code !== 'PGRST116') {
        console.warn(`Error checking category ${categoryId}:`, checkError.message);
        throw new Error(`Failed to check category existence: ${checkError.message}`);
      }

      if (existingCategory) {
        console.log(`✅ Category ${categoryId} exists`);
        return categoryId;
      }

      // Category doesn't exist - create it with the exact AI-provided category_id
      console.log(`🔧 Category ${categoryId} doesn't exist, creating it...`);

      const newCategory = {
        id: categoryId,
        title: this.generateCategoryTitle(categoryId),
        description: `AI-generated category for ${categoryId}`,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const { error: createError } = await supabaseAdmin
        .from('categories')
        .insert([newCategory]);

      if (createError) {
        console.error(`Failed to create category ${categoryId}:`, createError.message);
        throw new Error(`Failed to create category: ${createError.message}`);
      }

      console.log(`✅ Created new category: ${categoryId}`);
      return categoryId;

    } catch (error) {
      console.error(`Error validating category ${categoryId}:`, error);
      throw error; // Re-throw to fail the pipeline if category validation fails
    }
  }



  /**
   * Generate a human-readable title from category ID
   */
  private generateCategoryTitle(categoryId: string): string {
    return categoryId
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ')
      .toUpperCase();
  }



  /**
   * Update tool record in database
   */
  private async updateToolRecord(toolId: string, updates: any): Promise<void> {
    if (!supabaseAdmin) {
      throw new Error('Admin client not available - missing SUPABASE_SERVICE_ROLE_KEY');
    }

    const { error } = await supabaseAdmin
      .from('tools')
      .update(updates)
      .eq('id', toolId);

    if (error) {
      throw new Error(`Failed to update tool record: ${error.message}`);
    }
  }

  /**
   * Get pipeline status for a tool
   */
  async getStatus(toolId: string): Promise<PipelineResult | null> {
    if (!supabaseAdmin) {
      console.warn('Admin client not available - missing SUPABASE_SERVICE_ROLE_KEY');
      return null;
    }

    const { data: tool, error } = await supabaseAdmin
      .from('tools')
      .select(`
        *,
        editorial_reviews!editorial_reviews_tool_id_fkey (*)
      `)
      .eq('id', toolId)
      .single();

    if (error || !tool) {
      return null;
    }

    return {
      success: tool.ai_generation_status !== 'failed',
      toolId: tool.id,
      status: tool.ai_generation_status || 'pending',
      workflowState: this.determineWorkflowState(tool),
      generatedContent: tool.generated_content,
      qualityScore: tool.content_quality_score ? {
        overall: tool.content_quality_score,
        breakdown: {
          completeness: 0,
          accuracy: 0,
          engagement: 0,
          structure: 0,
          relevance: 0,
          consistency: 0
        },
        level: 'acceptable' as const,
        flags: [],
        suggestions: [],
        scoredAt: new Date().toISOString(),
        version: '1.0'
      } : undefined,
      editorialReview: tool.editorial_reviews?.[0],
      metadata: {
        startTime: tool.last_ai_update || tool.created_at,
        steps: []
      }
    };
  }

  /**
   * Determine current workflow state based on tool data
   */
  private determineWorkflowState(tool: any): WorkflowState {
    if (tool.content_status === 'published') {
      return 'published';
    }
    
    if (tool.editorial_reviews?.length > 0) {
      const review = tool.editorial_reviews[0];
      if (review.review_status === 'approved') {
        return 'approved';
      } else if (review.review_status === 'rejected') {
        return 'rejected';
      } else {
        return 'editorial_review';
      }
    }
    
    if (tool.ai_generation_status === 'processing') {
      return 'ai_generation';
    }
    
    if (tool.ai_generation_status === 'completed') {
      return 'manual_review';
    }
    
    return 'draft';
  }
}
