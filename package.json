{"name": "ai-dude-directory", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:memory": "set NODE_OPTIONS=--max-old-space-size=7168 && next dev", "build": "next build", "build:memory": "set NODE_OPTIONS=--max-old-space-size=7168 && next build", "start": "next start", "start:memory": "set NODE_OPTIONS=--max-old-space-size=7168 && next start", "lint": "next lint", "migrate": "tsx scripts/migrate-data.ts", "migrate:memory": "set NODE_OPTIONS=--max-old-space-size=7168 && tsx scripts/migrate-data.ts", "db:setup": "npm run migrate", "db:migrate": "tsx src/lib/database/migrate.ts up", "db:migrate:memory": "set NODE_OPTIONS=--max-old-space-size=7168 && tsx src/lib/database/migrate.ts up", "db:migrate:002": "tsx scripts/run-migration-002.ts", "db:migrate:003": "tsx scripts/apply-uniqueness-constraints-migration.ts", "db:migrate:versioning": "tsx scripts/run-versioning-migration.ts", "db:migrate:006": "tsx scripts/run-migration-006.ts", "db:migrate:007": "tsx scripts/run-migration-007.ts", "db:cleanup-functions": "tsx scripts/execute-function-cleanup.ts", "db:rollback": "tsx src/lib/database/migrate.ts down", "db:status": "tsx scripts/check-migration-status.ts", "db:show-sql": "tsx scripts/show-migration-sql.ts", "db:test-pg-trgm": "tsx scripts/test-pg-trgm-migration.ts", "test:jobs": "tsx scripts/test-jobs.ts", "test:bulk-after-migration": "tsx scripts/test-bulk-processing-after-migration.ts", "test:uuid-functions-fix": "tsx scripts/test-uuid-and-functions-fix.ts", "test:uuid-only": "tsx scripts/test-uuid-generation-only.ts", "test:complete-fix": "tsx scripts/test-complete-fix-verification.ts", "investigate:version-mismatch": "tsx scripts/investigate-version-mismatch.ts", "test:bulk-fixes": "tsx scripts/test-bulk-processing-fixes.ts", "debug:constraint": "tsx scripts/debug-constraint-issue.ts", "force:constraint-fix": "tsx scripts/force-constraint-fix.ts", "test:holistic-fix": "tsx scripts/test-holistic-bulk-fix.ts", "investigate:job-constraints": "tsx scripts/investigate-job-constraints.ts", "db:migrate:008": "tsx scripts/run-migration-008.ts", "db:migrate:010": "tsx scripts/run-migration-010.ts", "db:migrate:010:memory": "set NODE_OPTIONS=--max-old-space-size=7168 && tsx scripts/run-migration-010.ts", "db:migrate:011": "tsx scripts/run-migration-011.ts", "db:migrate:011:memory": "set NODE_OPTIONS=--max-old-space-size=7168 && tsx scripts/run-migration-011.ts", "test:job-constraints": "tsx scripts/test-job-constraint-fixes.ts", "test:enhanced-fallback": "tsx scripts/test-enhanced-fallback.ts", "test:screenshot-timeout": "tsx scripts/test-screenshot-timeout-fix.ts", "test:cost-optimizer": "tsx scripts/test-cost-optimizer-respect.ts", "test:tool-id-fix": "tsx scripts/test-tool-id-fix.ts", "analyze:photoai-quality": "tsx scripts/analyze-photoai-quality.ts", "test:quality-threshold-fix": "tsx scripts/test-quality-threshold-fix.ts", "test:column-name-fix": "tsx scripts/test-column-name-fix.ts", "test:media-collection-skip": "tsx scripts/test-media-collection-skip.ts", "test:comprehensive-fixes": "tsx scripts/test-comprehensive-fixes.ts", "test:photoai-data-structure": "tsx scripts/test-photoai-data-structure.ts", "test:error1-fixes": "tsx scripts/test-error1-fixes.ts", "test:version-mismatch-fix": "tsx scripts/test-version-mismatch-fix.ts", "test:final-fixes": "tsx scripts/test-final-fixes.ts", "test:remaining-issues-fix": "tsx scripts/test-remaining-issues-fix.ts", "test:editorial-and-dashboard-fixes": "tsx scripts/test-editorial-and-dashboard-fixes.ts", "test:editorial-display-fix": "tsx scripts/test-editorial-display-fix.ts", "test:approval-workflow-fix": "tsx scripts/test-approval-workflow-fix.ts", "test:complete-approval-workflow": "tsx scripts/test-complete-approval-workflow.ts", "test:final-remaining-fixes": "tsx scripts/test-final-remaining-fixes.ts", "test:jobs-filter-fix": "tsx scripts/test-jobs-filter-fix.ts", "fix:stuck-bulk-job": "tsx scripts/fix-stuck-bulk-job.ts", "test:display-fixes": "tsx scripts/test-display-fixes.ts", "cleanup:stuck-jobs": "tsx scripts/cleanup-stuck-jobs.ts", "test:final-job-fixes": "tsx scripts/test-final-job-fixes.ts", "test:complete-bulk-fix": "tsx scripts/test-complete-bulk-fix.ts", "test:archived-status-fix": "tsx scripts/test-archived-status-fix.ts", "test:archived-status-fix:memory": "set NODE_OPTIONS=--max-old-space-size=7168 && tsx scripts/test-archived-status-fix.ts", "test:short-description-fix": "tsx scripts/test-short-description-fix.ts", "test:automation": "tsx scripts/test-core-automation.ts", "test:e2e": "tsx tests/e2e/admin-workflows.test.ts", "health:check": "tsx scripts/production-health-check.ts", "inspect:data": "tsx scripts/inspect-scraped-data.ts", "demo:scraping": "tsx scripts/demo-scraping.ts", "production:verify": "npm run health:check && npm run test:e2e", "deploy:prepare": "npm run build && npm run production:verify", "monitor:start": "tsx scripts/production-monitor.ts", "cleanup:jobs": "tsx scripts/cleanup-completed-jobs.ts", "test:ai": "tsx src/lib/ai/test-dual-providers.ts", "ai:health": "tsx -e \"import { performHealthCheck } from './src/lib/ai'; performHealthCheck().then(console.log).catch(console.error)\"", "ai:validate": "tsx scripts/validate-ai-config.ts", "test:config": "tsx scripts/test-config-api.ts", "type:check": "node scripts/check-types.js", "type:validate": "tsx scripts/check-bulk-processing-types.ts", "migrate:execute": "tsx scripts/execute-data-migration.ts", "migrate:status": "tsx scripts/execute-data-migration.ts status", "migrate:rollback": "tsx scripts/execute-data-migration.ts rollback", "test:comprehensive": "tsx tests/comprehensive-test-runner.ts", "test:integration": "tsx tests/integration/enhanced-ai-system.test.ts", "test:performance": "tsx tests/performance/load-testing.ts", "test:ai-providers": "tsx tests/api/ai-providers.test.ts", "test:rollback": "tsx tests/migration/rollback-validation.test.ts", "test:system": "tsx tests/system-validation.ts", "setup:test-env": "tsx scripts/setup-test-environment.ts", "migrate:dry-run": "tsx scripts/execute-data-migration.ts execute --dry-run", "seed:tools": "tsx scripts/seed-sample-tools.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:admin": "jest --testPathPattern=admin"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@monaco-editor/react": "^4.7.0", "@supabase/supabase-js": "^2.50.0", "@types/animejs": "^3.1.13", "@types/jsonwebtoken": "^9.0.9", "animejs": "^4.0.2", "cheerio": "^1.1.0", "css-select": "^5.2.1", "css-select-base-adapter": "^0.1.1", "css-what": "^6.2.1", "dotenv": "^16.5.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.511.0", "metascraper": "^5.49.0", "metascraper-image": "^5.47.2", "metascraper-logo": "^5.47.2", "metascraper-logo-favicon": "^5.47.2", "next": "15.3.2", "nodemailer": "^6.9.8", "openai": "^5.1.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.57.0", "zod": "^3.25.64"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.20.7", "@babel/plugin-transform-class-properties": "^7.27.1", "@babel/plugin-transform-object-rest-spread": "^7.27.3", "@babel/plugin-transform-private-methods": "^7.27.1", "@babel/plugin-transform-private-property-in-object": "^7.27.1", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/node": "^20", "@types/node-fetch": "^2.6.12", "@types/nodemailer": "^6.4.14", "@types/react": "^19", "@types/react-dom": "^19", "babel-jest": "^30.0.0", "eslint": "^9", "eslint-config-next": "15.3.2", "ignore-loader": "^0.1.2", "jest": "^30.0.1", "jest-environment-jsdom": "^30.0.0", "msw": "^2.10.2", "supabase": "^2.26.9", "tailwindcss": "^4", "tsx": "^4.19.4", "typescript": "^5", "undici": "^7.10.0"}}