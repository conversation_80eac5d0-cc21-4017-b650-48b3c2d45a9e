#!/usr/bin/env tsx

/**
 * Migration 010 Runner: Add 'archived' status to bulk_processing_jobs constraint
 * 
 * This script executes Migration 010 which adds 'archived' as a valid status
 * for the bulk_processing_jobs table, resolving constraint violations in the
 * job cleanup service.
 */

import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { join } from 'path';

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:');
  console.error('   - NEXT_PUBLIC_SUPABASE_URL');
  console.error('   - SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function runMigration010() {
  console.log('🚀 Starting Migration 010: Add archived status to bulk_processing_jobs');
  console.log('📋 This migration will:');
  console.log('   • Drop existing bulk_processing_jobs status constraint');
  console.log('   • Add new constraint including "archived" status');
  console.log('   • Test the new constraint with verification queries');
  console.log('');

  try {
    // Read the migration SQL file
    const migrationPath = join(process.cwd(), 'src', 'lib', 'database', 'migrations', '010_add_archived_status_to_bulk_jobs.sql');
    const migrationSQL = readFileSync(migrationPath, 'utf-8');

    console.log('📄 Executing migration SQL...');
    
    // Execute the migration
    const { error } = await supabase.rpc('exec_sql', { sql: migrationSQL });
    
    if (error) {
      console.error('❌ Migration failed:', error);
      process.exit(1);
    }

    console.log('✅ Migration 010 executed successfully!');
    console.log('');

    // Verify the constraint was updated
    console.log('🔍 Verifying constraint update...');
    
    const { data: constraints, error: constraintError } = await supabase
      .rpc('exec_sql', { 
        sql: `
          SELECT
            conname as constraint_name,
            pg_get_constraintdef(oid) as constraint_definition
          FROM pg_constraint
          WHERE conname = 'bulk_processing_jobs_status_check';
        `
      });

    if (constraintError) {
      console.warn('⚠️ Could not verify constraint (migration may still be successful):', constraintError);
    } else {
      console.log('✅ Constraint verification completed');
      if (constraints && constraints.length > 0) {
        console.log('📋 Updated constraint definition:');
        console.log(`   ${constraints[0].constraint_definition}`);
      }
    }

    console.log('');
    console.log('🎉 Migration 010 completed successfully!');
    console.log('✅ Job cleanup service can now archive old jobs');
    console.log('✅ Constraint violation error should be resolved');

  } catch (error) {
    console.error('❌ Migration 010 failed with error:', error);
    process.exit(1);
  }
}

// Execute the migration
runMigration010().catch((error) => {
  console.error('❌ Unexpected error:', error);
  process.exit(1);
});
